const _ = require('lodash');
const async = require('async');
const Joi = require('joi');


const statisticsService = require('../../../services/statisticsService');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const config = require('config');
const fetch = require('node-fetch');

const { Configuration, OpenAIApi } = require("openai");

const configuration = new Configuration({
  apiKey: config.ai.apiKey,
  basePath: config.ai.url,
  baseOptions: {
    headers: {
      'Authorization': `Bearer ${config.ai.apiKey}`,
    },
  },
});

const client = new OpenAIApi(configuration);


module.exports = (req, res) => {
  let {
    type = 'week',
    endTime,
    summaryByArea
  } = req.body;
  let startTime;
  let statisticsData = {};
  let aiResponse = {};
  let timeText = '';
  let strategyText = '';
  let contextPrompt = '';

  const validateParams = (next) => {
    const options = ['3days', '7days', 'week', 'month', 'year'];
    if (type && !options.includes(type)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: `Tham số type không hợp lệ. Các giá trị hợp lệ là: ${options.join(', ')}`
        }
      });
    }
    if (!endTime) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,    
        message: {
          head: 'Lỗi tham số',
          body: 'Tham số endTime là bắt buộc'
        }
      });
    }

    // Tính toán startTime dựa theo endTime và type
    const endDate = new Date(endTime);
    
    switch (type) {
      case '3days':
        strategyText = '3 ngày gần nhất';
        startTime = new Date(endDate);
        startTime.setDate(endDate.getDate() - 2);
        break;
      case '7days':
        strategyText = '7 ngày gần nhất';
        startTime = new Date(endDate);
        startTime.setDate(endDate.getDate() - 6);
        break;
      case 'week':
        strategyText = 'tuần hiện tại';
        startTime = new Date(endDate);
        // Tính ngày Thứ 2 của tuần (1 = Monday)
        const dayOfWeek = endDate.getDay(); // 0=Sunday, 1=Monday, ..., 6=Saturday
        const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Nếu là CN thì lùi 6 ngày, ngược lại lùi (dayOfWeek - 1) ngày
        startTime.setDate(endDate.getDate() - daysToMonday);
        break;
      case 'month':
        strategyText = 'của các tuần trong tháng hiện tại';
        startTime = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
        break;
      case 'year':
        strategyText = 'của các tháng trong năm nay';
        startTime = new Date(endDate.getFullYear(), 0, 1);
        break;
      default:
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Lỗi tham số',
            body: `Tham số type không hợp lệ`
          }
        });
    }

    // Tạo timeText từ startTime và endTime
    const formatDate = (date) => {
      const day = String(date.getDate()).padStart(2, '0');
      const month = String(date.getMonth() + 1).padStart(2, '0');
      return `${day}/${month}`;
    };
    
    timeText = `Từ ${formatDate(startTime)} đến ${formatDate(endDate)}`;
    
    next();
  };

  const getStatisticsData = (next) => {
    // Xử lý dữ liệu summaryByArea nếu có
    if (summaryByArea && Array.isArray(summaryByArea)) {
      statisticsData.chiTietTheoKhuVuc = summaryByArea.map(area => ({
        tenKhuVuc: area.areaName,
        tongSoVu: area.total,
        theoLoaiVuViec: area.category ? area.category.map(cat => ({
          loaiVu: cat.categoryName,
          soLuong: cat.count
        })) : [],
        theoMocThoiGian: area.metrics ? area.metrics.map(metric => ({
          moc: metric.timeLabel,
          soVu: metric.totalReports
        })) : []
      }));
    }

    console.log('Statistics Data:', statisticsData);
    next();
  };


  const callAIStream = (next) => {
    contextPrompt = `
Dữ liệu đầu vào là số liệu thống kê trong ${strategyText} về an ninh trật tự theo khu vực ${timeText}.  

Dữ liệu:
${JSON.stringify(statisticsData, null, 2)}

Nhiệm vụ của bạn:
1. **So sánh giữa các khu vực**
   - Xác định khu vực có nhiều vụ việc nhất và ít nhất.
   - Tính tỷ lệ chênh lệch, chỉ ra "điểm nóng".

2. **Phân tích chi tiết các khu vực tiêu biểu (nhiều vụ việc nhất)**
   - Thống kê theo mốc thời gian.
   - Chỉ ra mốc cao điểm, thấp điểm, biến động.
   - Phân tích theo từng loại vụ việc (cháy, mâu thuẫn, vi phạm, TNGT).

3. **Nhận diện rủi ro & bất thường**
   - Lĩnh vực nào chiếm tỷ trọng lớn → nguy cơ chính.
   - Mốc/khu vực nào vượt ngưỡng trung bình.

4. **Khuyến nghị**
   - Đề xuất bố trí lực lượng, biện pháp phòng ngừa.

Hãy trình bày báo cáo chi tiết, văn phong hành chính – ngắn gọn, rõ ràng, có số liệu minh chứng.`;

    const url = `${config.ai.url}/chat/completions`;
    const headers = {
      'Authorization': `Bearer ${config.ai.apiKey}`,
      'Content-Type': 'application/json'
    };
    const body = JSON.stringify({
      model: config.ai.model,
      messages: [
        { role: "system", content: "Bạn là chuyên gia phân tích dữ liệu an ninh trật tự cho hệ thống IOC Công an." },
        { role: "user", content: contextPrompt }
      ],
      max_tokens: 60000,
      temperature: 0.2,
      stream: true
    });

    fetch(url, { method: 'POST', headers, body })
      .then(response => {
        if (!response.ok || !response.body) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: 'Không thể stream từ OpenAI'
          });
        }

        res.setHeader('Content-Type', 'text/event-stream; charset=utf-8');
        res.setHeader('Cache-Control', 'no-cache');
        res.setHeader('Connection', 'keep-alive');

        let fullContent = '';
        response.body.on('data', (chunk) => {
          const lines = chunk.toString().split('\n');
          lines.forEach(line => {
            if (line.startsWith('data:')) {
              const data = line.replace('data: ', '').trim();
              if (data === '[DONE]') {
                res.end();
              } else {
                try {
                  const json = JSON.parse(data);
                  const delta = json.choices?.[0]?.delta?.content || '';
                  if (delta) {
                    fullContent += delta;
                    res.write(delta);
                  }
                } catch (e) {}
              }
            }
          });
        });
        response.body.on('end', () => {
          aiResponse = { content: fullContent };
          next();
        });
        response.body.on('error', (err) => {
          next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: 'Lỗi stream OpenAI',
            error: err
          });
        });
      })
      .catch(error => {
        next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR,
          error
        });
      });
  };

  const returnResult = (next) => {  
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        report: aiResponse.content,
        timeText,
        contextPrompt,
        metadata: {
          statisticsData,
          aiModel: aiResponse.model,
          usage: aiResponse.usage,
          generatedAt: Date.now()
        }
      }
    });
  };

  async.waterfall([
    validateParams,
    getStatisticsData,
    callAIStream,
    returnResult
  ], (err, data) => {
    if (_.isError(err)) {
      global.logger && global.logger.logError([err], req.originalUrl, req.body);
      global.MailUtil && global.MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Nếu đã trả về qua stream thì không cần res.json nữa
    if (!res.headersSent) {
      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });
      res.json(data || err);
    }
  });
};
