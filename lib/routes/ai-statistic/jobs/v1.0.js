const _ = require('lodash');
const async = require('async');
const Joi = require('joi');


const statisticsService = require('../../../services/statisticsService');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const config = require('config');
const fetch = require('node-fetch');

const { Configuration, OpenAIApi } = require("openai");

const configuration = new Configuration({
  apiKey: config.ai.apiKey,
  basePath: config.ai.url,
  baseOptions: {
    headers: {
      'Authorization': `Bearer ${config.ai.apiKey}`,
    },
  },
});

const client = new OpenAIApi(configuration);


module.exports = (req, res) => {
  let {
    prompt = '',
    type = 'general', // 'general', 'attendance', 'performance'
    timeRange = '7days',
    unitIds = []
  } = req.body;


  let statisticsData = {
    "meta": {
      "don_vi_tinh": "lượt chấm/tuần",
      "thu_tu_ngay": ["T2", "T3", "T4", "T5", "T6", "T7", "CN"],
      "late_threshold_min": 5
    },
    "tong_ket": {
      "tong_luot": 181,
      "dung_gio": 145,
      "muon": 30,
      "chua_diem_danh": 6,
      "nghi_phep_cong_tac": 0
    },
    "theo_ngay": {
      "T2": { "dung_gio": 21, "muon": 6, "chua_diem_danh": 1, "nghi_phep_cong_tac": 0 },
      "T3": { "dung_gio": 18, "muon": 5, "chua_diem_danh": 1, "nghi_phep_cong_tac": 0 },
      "T4": { "dung_gio": 22, "muon": 4, "chua_diem_danh": 1, "nghi_phep_cong_tac": 0 },
      "T5": { "dung_gio": 25, "muon": 5, "chua_diem_danh": 1, "nghi_phep_cong_tac": 0 },
      "T6": { "dung_gio": 26, "muon": 6, "chua_diem_danh": 1, "nghi_phep_cong_tac": 0 },
      "T7": { "dung_gio": 18, "muon": 2, "chua_diem_danh": 1, "nghi_phep_cong_tac": 0 },
      "CN": { "dung_gio": 15, "muon": 2, "chua_diem_danh": 0, "nghi_phep_cong_tac": 0 }
    },
    "top_ca_nhan_muon": [
      { "id": "CB001", "ten": "Nguyễn Văn A", "don_vi": "Tổ An ninh", "so_lan_muon": 9 },
      { "id": "CB002", "ten": "Trần Thị B", "don_vi": "Tổ CSGT", "so_lan_muon": 8 },
      { "id": "CB003", "ten": "Phạm Văn C", "don_vi": "Tổ Tổng hợp", "so_lan_muon": 7 },
      { "id": "CB004", "ten": "Lê Thị D", "don_vi": "Tổ CSKV", "so_lan_muon": 6 }
    ],
    "top_ca_nhan_chua_diem_danh": [
      { "id": "CB005", "ten": "Đỗ Văn E", "don_vi": "Tổ Cảnh sát trật tự", "so_lan_khong_cham": 3 },
      { "id": "CB006", "ten": "Hoàng Thị F", "don_vi": "Tổ Hành chính", "so_lan_khong_cham": 2 },
      { "id": "CB007", "ten": "Vũ Văn G", "don_vi": "Tổ CSKV", "so_lan_khong_cham": 1 }
    ],
    "ghi_chu": "Tổng 181 = 145 đúng giờ + 30 muộn + 6 chưa điểm danh + 0 nghỉ. Mỗi ngày cộng các trạng thái đúng bằng tổng tuần tương ứng."
  };
  let aiResponse = {};

  const validateParams = (next) => {
    const schema = Joi.object({
      prompt: Joi.string().required().min(10).max(1000),
      type: Joi.string().valid('general', 'attendance', 'performance').default('general'),
      timeRange: Joi.string().valid('1day', '7days', '30days', '90days').default('7days'),
      unitIds: Joi.array().items(Joi.string()).default([])
    });

    const { error, value } = schema.validate({ prompt, type, timeRange, unitIds });
    
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: error.details[0].message
      });
    }

    // Update validated values
    prompt = value.prompt;
    type = value.type;
    timeRange = value.timeRange;
    unitIds = value.unitIds;

    next();
  };

  const getStatisticsData = (next) => {
    // Sử dụng dữ liệu mẫu đã có sẵn
    next();
  };


  const callAIStream = (next) => {
    const contextPrompt = `
Dữ liệu đầu vào là số liệu thống kê tình trạng cán bộ đi làm trong tuần.
Dữ liệu:
${JSON.stringify(statisticsData, null, 2)}

Nhiệm vụ:
1) Tổng quan & KPI
   - Tính tỷ lệ đúng giờ, muộn, chưa điểm danh, nghỉ phép/công tác trên tổng lượt chấm.
   - So sánh với ngưỡng SLA (ví dụ: đúng giờ ≥ 90%, muộn ≤ 5%). Đánh giá đạt/không đạt.

2) Xu hướng theo ngày (T2–CN)
   - Nêu ngày cao điểm đi muộn/không điểm danh, ngày tốt nhất.
   - Chỉ ra biến động (tăng/giảm) so với trung bình tuần và độ lệch chuẩn.

3) Phân tích theo cá nhân/đơn vị
   - Liệt kê top cán bộ đi muộn nhiều nhất, top chưa điểm danh nhiều nhất.
   - Phân loại: tái phạm (≥ X lần/tuần) vs. phát sinh ngẫu nhiên.
   - Nếu có “đi muộn chuỗi” (streak liên tiếp ngày), đánh dấu cảnh báo.

4) Nguyên nhân khả dĩ & tác động
   - Giả thuyết theo khung giờ/ca trực/khu vực/loại nhiệm vụ.
   - Tác động đến trực ban, điều động lực lượng.

5) Khuyến nghị & cảnh báo tự động
   - Biện pháp ngắn hạn, trung hạn, dài hạn (nhắc nhở, xác thực kép, tự động hóa quy trình).
   - Đặt ngưỡng cảnh báo (ví dụ: cá nhân muộn ≥ 3 lần/tuần; ngày có muộn > trung bình + 1σ).

Trình bày ngắn gọn, rõ ràng, có số liệu minh chứng và gạch đầu dòng`;

    const url = `${config.ai.url}/chat/completions`;
    const headers = {
      'Authorization': `Bearer ${config.ai.apiKey}`,
      'Content-Type': 'application/json'
    };
    const body = JSON.stringify({
      model: config.ai.model,
      messages: [
        { role: "system", content: "Bạn là chuyên gia phân tích kỷ luật điểm danh cho hệ thống IOC Công an." },
        { role: "user", content: contextPrompt }
      ],
      max_tokens: 60000,
      temperature: 0.2,
      stream: true
    });

    fetch(url, { method: 'POST', headers, body })
      .then(response => {
        if (!response.ok || !response.body) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: 'Không thể stream từ OpenAI'
          });
        }

        res.setHeader('Content-Type', 'text/event-stream; charset=utf-8');
        res.setHeader('Cache-Control', 'no-cache');
        res.setHeader('Connection', 'keep-alive');

        let fullContent = '';
        response.body.on('data', (chunk) => {
          const lines = chunk.toString().split('\n');
          lines.forEach(line => {
            if (line.startsWith('data:')) {
              const data = line.replace('data: ', '').trim();
              if (data === '[DONE]') {
                res.end();
              } else {
                try {
                  const json = JSON.parse(data);
                  const delta = json.choices?.[0]?.delta?.content || '';
                  if (delta) {
                    fullContent += delta;
                    res.write(delta);
                  }
                } catch (e) {}
              }
            }
          });
        });
        response.body.on('end', () => {
          aiResponse = { content: fullContent };
          next();
        });
        response.body.on('error', (err) => {
          next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: 'Lỗi stream OpenAI',
            error: err
          });
        });
      })
      .catch(error => {
        next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR,
          error
        });
      });
  };

  const returnResult = (next) => {  
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        report: aiResponse.content,
        metadata: {
          type,
          timeRange,
          unitIds,
          prompt,
          statisticsData,
          aiModel: aiResponse.model,
          usage: aiResponse.usage,
          generatedAt: Date.now()
        }
      }
    });
  };

  async.waterfall([
    // validateParams,
    getStatisticsData,
    callAIStream,
    returnResult
  ], (err, data) => {
    if (_.isError(err)) {
      global.logger && global.logger.logError([err], req.originalUrl, req.body);
      global.MailUtil && global.MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Nếu đã trả về qua stream thì không cần res.json nữa
    if (!res.headersSent) {
      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });
      res.json(data || err);
    }
  });
};
